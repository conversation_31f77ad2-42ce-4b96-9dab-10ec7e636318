import { ITestData } from "@/app/core/test";
import Step from "@/app/core/step";
import h from "@/app/helpers/all";
import path from "path";

/**
 * Interface defining the structure of the configuration object
 * used by the Report class. This extends the base configuration
 * to include report-specific properties.
 */
interface ReportConfig {
  /** Path to the SDT Excel file */
  sdtFilePath?: string;
  /** Base path where results folders will be created */
  resultsFolderPath?: string;
  /** Generated folder name for this test run (set by setFolder) */
  resultsFolderName?: string;
  /** Generated Excel filename for results (set by setFolder) */
  resultsFileName?: string;
  /** Temporary folder path for screenshots during test execution */
  resultsScreenshotsTempFolder?: string;
  /** Additional configuration properties */
  [key: string]: any;
}

/**
 * Interface for the data structure passed to the writeResults task
 */
interface WriteResultsData {
  /** Path to the source SDT Excel file */
  sdtFilePath: string;
  /** Array of executed test results with filtered steps */
  tests: ITestData[];
  /** Output folder path for results */
  outputFolderName: string;
  /** Output Excel filename */
  outputFileName: string;
}

/**
 * Report class handles the generation and management of test execution reports.
 *
 * This class is responsible for:
 * - Creating timestamped result folders
 * - Filtering and organizing test execution data
 * - Moving screenshots and videos to the results folder
 * - Generating Excel reports with test results
 *
 * The class integrates with Cypress tasks for file operations and uses
 * the SDT framework's global state for accessing test results.
 */
export default class Report {
  /**
   * Creates a new Report instance
   * @param config - Configuration object containing paths and settings
   */
  constructor(private config: ReportConfig) {
    this.validateConfig();
  }

  /**
   * Validates that the required configuration properties are present
   * @throws {Error} If required configuration is missing
   */
  private validateConfig(): void {
    // Note: Properties are optional in the interface to accommodate
    // the flexible nature of the SDT configuration system.
    // Validation is performed at runtime when methods are called.

    // Basic validation - ensure config object exists
    if (!this.config || typeof this.config !== "object") {
      throw new Error("Report configuration must be a valid object");
    }
  }

  /**
   * Sets up the results folder structure and filenames for the current test run.
   *
   * This method:
   * - Generates a timestamp-based folder name
   * - Creates the base results folder if it doesn't exist
   * - Creates the specific test run folder
   * - Sets the Excel filename for the results
   *
   * The timestamp format is: YYYY-MM-DDTHH.MM.SS
   *
   * @throws {Error} If folder creation fails
   */
  setFolder(): void {
    try {
      // Validate required properties for this operation
      if (!this.config.resultsFolderPath) {
        throw new Error(
          "resultsFolderPath is required for setFolder operation"
        );
      }

      const testDateTime = h.getLocalDateAndTime();

      // Set the timestamped folder path
      this.config.resultsFolderName = path.join(
        this.config.resultsFolderPath,
        testDateTime
      );

      // Set the Excel filename with timestamp
      this.config.resultsFileName = `${testDateTime}.xlsx`;

      // Create the base results folder
      cy.task("createFolder", this.config.resultsFolderPath);

      // Create the specific test run folder
      cy.task("createFolder", this.config.resultsFolderName);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to set up results folder: ${errorMessage}`);
    }
  }

  /**
   * Writes the test execution results to files and organizes output artifacts.
   *
   * This method performs the following operations:
   * 1. Filters executed tests to include only valid Step instances
   * 2. Moves screenshots from temp folder to results folder
   * 3. Cleans up temporary screenshot folder
   * 4. Generates Excel report with test results
   * 5. Moves Cypress video recording to results folder
   *
   * @throws {Error} If any file operation or report generation fails
   */
  write(): void {
    try {
      // Validate that required properties are available
      if (!this.config.sdtFilePath) {
        throw new Error("sdtFilePath is required for write operation");
      }

      // Validate that required folders are set
      if (!this.config.resultsFolderName || !this.config.resultsFileName) {
        throw new Error(
          "Results folder not initialized. Call setFolder() first."
        );
      }

      // Filter executed tests to include only valid Step instances
      // This ensures that only properly executed steps are included in the report
      const tests: TestData[] = Cypress.sdt.results.executedTests.map(
        (test: any) => {
          // Create a copy to avoid mutating the original test data
          const testCopy = { ...test };
          testCopy.steps =
            test.steps?.filter((step: any) => step instanceof Step) || [];
          return testCopy;
        }
      );

      // Prepare data for the Excel report generation
      const writeResultsData: WriteResultsData = {
        sdtFilePath: this.config.sdtFilePath!,
        tests,
        outputFolderName: this.config.resultsFolderName!,
        outputFileName: this.config.resultsFileName!,
      };

      // Move screenshots from temporary folder to results folder
      if (this.config.resultsScreenshotsTempFolder) {
        cy.task("moveFiles", {
          sourceFolder: this.config.resultsScreenshotsTempFolder,
          destinationFolder: this.config.resultsFolderName,
        });

        // Clean up temporary screenshot folder
        cy.task("deleteFolder", this.config.resultsScreenshotsTempFolder);
      }

      // Generate the Excel report with test results
      cy.task("writeResults", writeResultsData);

      // Move Cypress video recording to results folder
      this.moveVideoFile();
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      throw new Error(`Failed to write test results: ${errorMessage}`);
    }
  }

  /**
   * Moves the Cypress video recording to the results folder.
   *
   * The video file is renamed to 'video.mp4' in the results folder
   * for consistent naming across test runs.
   *
   * @private
   */
  private moveVideoFile(): void {
    const videoFilePath = "cypress/videos/runSdt.mp4";
    const destinationFilePath = path.join(
      this.config.resultsFolderName!,
      "video.mp4"
    );

    cy.task("moveFile", {
      sourceFilePath: videoFilePath,
      destinationFilePath,
    });
  }
}
